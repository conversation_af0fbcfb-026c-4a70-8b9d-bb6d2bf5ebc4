# Valdi vs Flutter: A Comprehensive Guide for Flutter Developers

## Table of Contents

1. [Introduction](#introduction)
2. [Architecture Comparison](#architecture-comparison)
3. [Language Translation Guide](#language-translation-guide)
4. [Development Workflow Comparison](#development-workflow-comparison)
5. [Practical Migration Examples](#practical-migration-examples)
6. [Getting Started for Flutter Developers](#getting-started-for-flutter-developers)
7. [Common Pitfalls and Solutions](#common-pitfalls-and-solutions)

## Introduction

Welcome, Flutter developers! This guide is designed specifically for developers with solid Dart experience who want to transition to Valdi. We'll use your existing Flutter knowledge as a foundation and show you how Valdi concepts map to what you already know.

**What You'll Learn:**
- How Valdi's TypeScript + JSX approach compares to Dart + Widget composition
- How Valdi's compilation pipeline differs from Flutter's
- Practical code examples showing Flutter patterns → Valdi equivalents
- Step-by-step migration strategies

**Assumptions:**
- You're comfortable with Flutter concepts like `StatefulWidget`, `StatelessWidget`, `BuildContext`, `setState()`
- You understand Flutter's widget tree and rendering pipeline
- You have zero JavaScript/TypeScript experience (we'll teach you!)

## Architecture Comparison

### Compilation Pipeline: Dart vs TypeScript

**Flutter's Compilation Flow:**
```
Dart Source → Dart Analyzer → Dart Compiler → Native ARM/x64 Code
```

**Valdi's Compilation Flow:**
```
TypeScript/TSX → Valdi Compiler → JavaScript/Bytecode/Native → Mobile App
```

#### Key Differences:

| Aspect | Flutter | Valdi |
|--------|---------|-------|
| **Source Language** | Dart | TypeScript + JSX |
| **Compilation Modes** | JIT (debug) / AOT (release) | js, js_bytecode, native |
| **Hot Reload** | Dart VM hot reload | JavaScript hot reload |
| **Type System** | Dart's sound null safety | TypeScript's structural typing |
| **Build Tool** | Flutter CLI + Dart | Valdi CLI + Bazel |

### Component System: Widgets vs Components

**Flutter Widget Hierarchy:**
```dart
Widget → StatelessWidget/StatefulWidget → Your Custom Widgets
```

**Valdi Component Hierarchy:**
```typescript
Component → Component<ViewModel>/StatefulComponent<ViewModel, State> → Your Custom Components
```

#### Side-by-Side Comparison:

**Flutter StatelessWidget:**
```dart
class MyWidget extends StatelessWidget {
  final String title;
  
  const MyWidget({Key? key, required this.title}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      child: Text(title),
    );
  }
}
```

**Valdi Component (Equivalent):**
```typescript
interface MyComponentViewModel {
  title: string;
}

class MyComponent extends Component<MyComponentViewModel> {
  onRender(): void {
    <view padding={16}>
      <label value={this.viewModel.title} />
    </view>;
  }
}
```

### Rendering Pipeline Comparison

**Flutter Rendering:**
```
Widget.build() → Element Tree → RenderObject Tree → Painting → Compositing
```

**Valdi Rendering:**
```
Component.onRender() → JSX → ViewNode Tree → Native Views → Platform Rendering
```

#### Key Similarities:
- Both use virtual trees for efficient updates
- Both perform reconciliation to minimize native operations
- Both support hot reload for fast development

#### Key Differences:
- Flutter uses Skia for custom rendering; Valdi uses native platform views
- Flutter's layout uses its own constraint-based system; Valdi uses Flexbox (Yoga)
- Flutter rebuilds widget subtrees; Valdi re-renders component subtrees

## Language Translation Guide

### TypeScript Basics for Dart Developers

Since you know Dart, learning TypeScript will feel familiar. Here are the key mappings:

#### Type Declarations

**Dart:**
```dart
String name = "Hello";
int count = 42;
bool isVisible = true;
List<String> items = ["a", "b", "c"];
Map<String, int> scores = {"alice": 100, "bob": 85};
```

**TypeScript:**
```typescript
let name: string = "Hello";
let count: number = 42;
let isVisible: boolean = true;
let items: string[] = ["a", "b", "c"];
let scores: { [key: string]: number } = {"alice": 100, "bob": 85};
```

#### Interfaces and Classes

**Dart:**
```dart
abstract class Animal {
  String get name;
  void makeSound();
}

class Dog implements Animal {
  final String name;
  
  Dog(this.name);
  
  @override
  void makeSound() {
    print("Woof!");
  }
}
```

**TypeScript:**
```typescript
interface Animal {
  name: string;
  makeSound(): void;
}

class Dog implements Animal {
  constructor(public name: string) {}
  
  makeSound(): void {
    console.log("Woof!");
  }
}
```

#### Null Safety

**Dart (Null Safety):**
```dart
String? nullableName;
String nonNullableName = "Required";

// Null-aware operators
String displayName = nullableName ?? "Unknown";
int? length = nullableName?.length;
```

**TypeScript:**
```typescript
let nullableName: string | undefined;
let nonNullableName: string = "Required";

// Null checking
let displayName = nullableName ?? "Unknown";
let length = nullableName?.length;
```

### JSX Syntax for Flutter Developers

JSX is Valdi's equivalent to Flutter's widget composition. Think of JSX elements as Flutter widgets:

**Flutter Widget Composition:**
```dart
Container(
  padding: EdgeInsets.all(16),
  decoration: BoxDecoration(
    color: Colors.blue,
    borderRadius: BorderRadius.circular(8),
  ),
  child: Column(
    children: [
      Text("Hello"),
      Text("World"),
    ],
  ),
)
```

**Valdi JSX (Equivalent):**
```typescript
<view 
  padding={16} 
  backgroundColor="blue" 
  borderRadius={8}
>
  <label value="Hello" />
  <label value="World" />
</view>
```

#### Key JSX Rules:
1. **Self-closing tags:** `<image />` (like Flutter's `Image()`)
2. **Attributes use camelCase:** `backgroundColor` not `background-color`
3. **JavaScript expressions in braces:** `{this.state.count}` not `${state.count}`
4. **No return statement needed:** JSX is automatically returned

## Development Workflow Comparison

### Project Setup and Build Commands

**Flutter Workflow:**
```bash
# Create project
flutter create my_app
cd my_app

# Run on device
flutter run

# Hot reload (automatic in debug mode)
# Press 'r' in terminal or save file

# Build for release
flutter build apk        # Android
flutter build ios        # iOS
```

**Valdi Workflow:**
```bash
# Create project
mkdir my_app && cd my_app
valdi bootstrap

# Run on device
valdi install android    # Android
valdi install ios        # iOS

# Start hot reload
valdi hotreload

# Build happens automatically during install
```

### Hot Reload Capabilities

| Feature | Flutter | Valdi |
|---------|---------|-------|
| **Code Changes** | Preserves app state | Preserves app state |
| **UI Updates** | Instant | Instant |
| **State Reset** | Manual (hot restart) | Manual (app restart) |
| **Supported Changes** | Most UI changes | TypeScript/JSX changes |
| **Trigger** | Save file or 'r' key | Save file |

### Package Management

**Flutter (pubspec.yaml):**
```yaml
dependencies:
  flutter:
    sdk: flutter
  http: ^0.13.5
  provider: ^6.0.5

dev_dependencies:
  flutter_test:
    sdk: flutter
```

**Valdi (module.yaml):**
```yaml
dependencies:
  - valdi_core
  - valdi_tsx
  - coreutils

web:
  dependencies:
    axios: "^1.0.0"
    lodash: "^4.17.21"
```

### Debugging Tools

**Flutter Debugging:**
- Flutter Inspector (widget tree)
- Dart DevTools (performance, memory)
- VS Code debugger with breakpoints
- `print()` statements and logging

**Valdi Debugging:**
- Valdi Inspector (component tree)
- Hermes Debugger (JavaScript debugging)
- VS Code debugger with breakpoints
- `console.log()` statements and logging

## Practical Migration Examples

### State Management: setState() Comparison

**Flutter StatefulWidget:**
```dart
class Counter extends StatefulWidget {
  @override
  _CounterState createState() => _CounterState();
}

class _CounterState extends State<Counter> {
  int _count = 0;

  void _increment() {
    setState(() {
      _count++;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text('Count: $_count'),
        ElevatedButton(
          onPressed: _increment,
          child: Text('Increment'),
        ),
      ],
    );
  }
}
```

**Valdi StatefulComponent:**
```typescript
interface CounterState {
  count: number;
}

class Counter extends StatefulComponent<{}, CounterState> {
  state: CounterState = {
    count: 0,
  };

  private increment = (): void => {
    this.setState({
      count: this.state.count + 1,
    });
  };

  onRender(): void {
    <view>
      <label value={`Count: ${this.state.count}`} />
      <view onTap={this.increment} backgroundColor="blue" padding={12}>
        <label value="Increment" color="white" />
      </view>
    </view>;
  }
}
```

### Navigation Patterns

**Flutter Navigation:**
```dart
// Push new screen
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => DetailScreen()),
);

// Pop back
Navigator.pop(context);
```

**Valdi Navigation:**
```typescript
// Push new screen
this.context.navigator.pushComponent({
  componentPath: "DetailScreen@my_module/DetailScreen",
  componentViewModel: {},
  componentContext: {},
}, true);

// Pop back
this.context.navigator.pop(true);
```

### Common UI Components

#### Button Component

**Flutter:**
```dart
ElevatedButton(
  onPressed: () => print("Tapped"),
  style: ElevatedButton.styleFrom(
    backgroundColor: Colors.blue,
    padding: EdgeInsets.all(16),
  ),
  child: Text("Tap Me"),
)
```

**Valdi:**
```typescript
<view 
  onTap={() => console.log("Tapped")}
  backgroundColor="blue"
  padding={16}
  borderRadius={8}
>
  <label value="Tap Me" color="white" />
</view>
```

#### List/ScrollView

**Flutter:**
```dart
ListView.builder(
  itemCount: items.length,
  itemBuilder: (context, index) {
    return ListTile(
      title: Text(items[index].title),
      subtitle: Text(items[index].subtitle),
    );
  },
)
```

**Valdi:**
```typescript
<scroll>
  {this.viewModel.items.map(item => 
    <view padding={16} key={item.id}>
      <label value={item.title} font={systemBoldFont(16)} />
      <label value={item.subtitle} font={systemFont(14)} />
    </view>
  )}
</scroll>
```

## Getting Started for Flutter Developers

### Step 1: Install Valdi

```bash
# Clone the Valdi repository
git clone https://github.com/Snapchat/Valdi.git
cd Valdi

# Install dependencies
npm install
npm run cli:install

# Set up development environment
valdi dev_setup
```

### Step 2: Create Your First Valdi App

```bash
# Create new project
mkdir my_first_valdi_app
cd my_first_valdi_app

# Bootstrap project
valdi bootstrap

# Open in VS Code
code .
```

### Step 3: Build Your First Component

Create a simple counter app to get familiar with Valdi syntax:

```typescript
// modules/my_app/src/CounterApp.tsx
import { StatefulComponent } from 'valdi_core/src/Component';
import { systemFont } from 'valdi_core/src/SystemFont';

interface CounterState {
  count: number;
}

export class CounterApp extends StatefulComponent<{}, CounterState> {
  state: CounterState = {
    count: 0,
  };

  private increment = (): void => {
    this.setState({ count: this.state.count + 1 });
  };

  private decrement = (): void => {
    this.setState({ count: this.state.count - 1 });
  };

  onRender(): void {
    <view 
      backgroundColor="white" 
      width="100%" 
      height="100%" 
      justifyContent="center" 
      alignItems="center"
    >
      <label 
        value={`Count: ${this.state.count}`} 
        font={systemFont(24)} 
        marginBottom={20}
      />
      
      <view flexDirection="row">
        <view 
          onTap={this.decrement}
          backgroundColor="red"
          padding={16}
          marginRight={10}
          borderRadius={8}
        >
          <label value="-" color="white" font={systemFont(20)} />
        </view>
        
        <view 
          onTap={this.increment}
          backgroundColor="green"
          padding={16}
          marginLeft={10}
          borderRadius={8}
        >
          <label value="+" color="white" font={systemFont(20)} />
        </view>
      </view>
    </view>;
  }
}
```

### Step 4: Run Your App

```bash
# Start hot reload
valdi hotreload

# In another terminal, install on device
valdi install ios     # or android
```

## Common Pitfalls and Solutions

### 1. **Forgetting Semicolons in JSX**

**❌ Wrong:**
```typescript
onRender(): void {
  <view>
    <label value="Hello" />
  </view>  // Missing semicolon!
}
```

**✅ Correct:**
```typescript
onRender(): void {
  <view>
    <label value="Hello" />
  </view>;  // Semicolon required!
}
```

### 2. **Using Dart-style String Interpolation**

**❌ Wrong (Dart style):**
```typescript
<label value="Count: ${this.state.count}" />
```

**✅ Correct (JavaScript style):**
```typescript
<label value={`Count: ${this.state.count}`} />
```

### 3. **Forgetting to Bind Event Handlers**

**❌ Wrong:**
```typescript
<view onTap={this.handleTap}>  // 'this' will be undefined
```

**✅ Correct:**
```typescript
private handleTap = (): void => {  // Arrow function binds 'this'
  // ...
};

// Or use bind:
<view onTap={this.handleTap.bind(this)}>
```

### 4. **Mixing Flutter and Valdi Concepts**

**❌ Wrong:**
```typescript
// Don't try to use Flutter concepts
this.build()  // No build() method in Valdi
setState(() => { ... })  // No callback in Valdi setState
```

**✅ Correct:**
```typescript
// Use Valdi patterns
this.onRender()  // Valdi's render method
this.setState({ ... })  // Direct object merge
```

### 5. **Incorrect TypeScript Types**

**❌ Wrong:**
```typescript
let items: Array = [];  // Missing type parameter
let callback: Function;  // Too generic
```

**✅ Correct:**
```typescript
let items: string[] = [];  // Specific array type
let callback: () => void;  // Specific function signature
```

## Next Steps

Now that you understand the basics, explore these advanced topics:

1. **[Valdi Architecture](./architecture.md)** - Deep dive into how Valdi works internally
2. **[Component Lifecycle](./core-component.md)** - Master component lifecycle methods
3. **[State Management](./core-states.md)** - Advanced state management patterns
4. **[Navigation](./valdi_navigation.md)** - Build multi-screen applications
5. **[Performance Optimization](./performance-optimization.md)** - Make your apps lightning fast

## Advanced Concepts for Flutter Developers

### Lifecycle Methods Comparison

**Flutter Lifecycle:**
```dart
class MyWidget extends StatefulWidget {
  @override
  _MyWidgetState createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> {
  @override
  void initState() {
    super.initState();
    // Initialize state
  }

  @override
  void didUpdateWidget(MyWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Handle prop changes
  }

  @override
  void dispose() {
    super.dispose();
    // Cleanup
  }

  @override
  Widget build(BuildContext context) {
    return Container();
  }
}
```

**Valdi Lifecycle:**
```typescript
class MyComponent extends StatefulComponent<ViewModel, State> {
  onCreate(): void {
    // Initialize component (like initState)
  }

  onViewModelUpdate(previousViewModel: ViewModel): void {
    // Handle prop changes (like didUpdateWidget)
  }

  onDestroy(): void {
    // Cleanup (like dispose)
  }

  onRender(): void {
    // Render UI (like build)
    <view />;
  }
}
```

### Form Handling Patterns

**Flutter Form:**
```dart
class MyForm extends StatefulWidget {
  @override
  _MyFormState createState() => _MyFormState();
}

class _MyFormState extends State<MyForm> {
  final _formKey = GlobalKey<FormState>();
  String _email = '';
  String _password = '';

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          TextFormField(
            decoration: InputDecoration(labelText: 'Email'),
            onChanged: (value) => setState(() => _email = value),
            validator: (value) => value?.isEmpty == true ? 'Required' : null,
          ),
          TextFormField(
            decoration: InputDecoration(labelText: 'Password'),
            obscureText: true,
            onChanged: (value) => setState(() => _password = value),
          ),
          ElevatedButton(
            onPressed: () {
              if (_formKey.currentState?.validate() == true) {
                // Submit form
              }
            },
            child: Text('Submit'),
          ),
        ],
      ),
    );
  }
}
```

**Valdi Form:**
```typescript
interface FormState {
  email: string;
  password: string;
  errors: { [key: string]: string };
}

class MyForm extends StatefulComponent<{}, FormState> {
  state: FormState = {
    email: '',
    password: '',
    errors: {},
  };

  private validateEmail = (email: string): string | null => {
    return email.length === 0 ? 'Email is required' : null;
  };

  private handleEmailChange = (email: string): void => {
    this.setState({
      email,
      errors: { ...this.state.errors, email: this.validateEmail(email) || '' },
    });
  };

  private handlePasswordChange = (password: string): void => {
    this.setState({ password });
  };

  private handleSubmit = (): void => {
    const emailError = this.validateEmail(this.state.email);
    if (!emailError) {
      // Submit form
      console.log('Submitting:', this.state.email, this.state.password);
    }
  };

  onRender(): void {
    <view padding={20}>
      {/* Email Input */}
      <view marginBottom={16}>
        <label value="Email" font={systemFont(14)} marginBottom={4} />
        <textfield
          value={this.state.email}
          onEditText={this.handleEmailChange}
          placeholder="Enter your email"
          borderWidth={1}
          borderColor={this.state.errors.email ? 'red' : 'gray'}
          padding={12}
          borderRadius={4}
        />
        {this.state.errors.email && (
          <label value={this.state.errors.email} color="red" font={systemFont(12)} />
        )}
      </view>

      {/* Password Input */}
      <view marginBottom={16}>
        <label value="Password" font={systemFont(14)} marginBottom={4} />
        <textfield
          value={this.state.password}
          onEditText={this.handlePasswordChange}
          placeholder="Enter your password"
          secureTextEntry={true}
          borderWidth={1}
          borderColor="gray"
          padding={12}
          borderRadius={4}
        />
      </view>

      {/* Submit Button */}
      <view
        onTap={this.handleSubmit}
        backgroundColor="blue"
        padding={16}
        borderRadius={8}
        alignItems="center"
      >
        <label value="Submit" color="white" font={systemBoldFont(16)} />
      </view>
    </view>;
  }
}
```

### Animation Patterns

**Flutter Animation:**
```dart
class AnimatedContainer extends StatefulWidget {
  @override
  _AnimatedContainerState createState() => _AnimatedContainerState();
}

class _AnimatedContainerState extends State<AnimatedContainer>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(_controller);
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTap: () {
              if (_controller.isCompleted) {
                _controller.reverse();
              } else {
                _controller.forward();
              }
            },
            child: Container(
              width: 100,
              height: 100,
              color: Colors.blue,
            ),
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
```

**Valdi Animation:**
```typescript
interface AnimatedBoxState {
  scale: number;
  isAnimating: boolean;
}

class AnimatedBox extends StatefulComponent<{}, AnimatedBoxState> {
  state: AnimatedBoxState = {
    scale: 1.0,
    isAnimating: false,
  };

  private handleTap = (): void => {
    if (this.state.isAnimating) return;

    const newScale = this.state.scale === 1.0 ? 1.2 : 1.0;

    this.setStateAnimatedPromise(
      { scale: newScale, isAnimating: true },
      {
        duration: 300,
        curve: 'easeInOut',
      }
    ).then(() => {
      this.setState({ isAnimating: false });
    });
  };

  onRender(): void {
    <view
      onTap={this.handleTap}
      width={100}
      height={100}
      backgroundColor="blue"
      transform={`scale(${this.state.scale})`}
    />;
  }
}
```

### Performance Optimization Patterns

**Flutter Performance Tips:**
```dart
// Use const constructors
const Text('Static text');

// Implement shouldRebuild for custom widgets
class OptimizedWidget extends StatelessWidget {
  const OptimizedWidget({Key? key, required this.data}) : super(key: key);

  final String data;

  @override
  Widget build(BuildContext context) {
    return Text(data);
  }
}

// Use ListView.builder for large lists
ListView.builder(
  itemCount: items.length,
  itemBuilder: (context, index) => ItemWidget(items[index]),
);
```

**Valdi Performance Tips:**
```typescript
// Use layout nodes instead of view for non-interactive containers
class OptimizedComponent extends Component<ViewModel> {
  onRender(): void {
    <layout>  {/* Cheaper than <view> for layout-only containers */}
      <label value={this.viewModel.data} />
    </layout>;
  }
}

// Use lazy loading for large lists with Lazy component
import { Lazy } from 'valdi_core/src/Lazy';

class OptimizedList extends Component<{ items: Item[] }> {
  onRender(): void {
    <scroll>
      {this.viewModel.items.map(item =>
        <Lazy key={item.id} estimatedHeight={60}>
          <ItemComponent item={item} />
        </Lazy>
      )}
    </scroll>;
  }
}

// Avoid creating new objects/arrays in render
class OptimizedParent extends Component<{ items: string[] }> {
  // ❌ Wrong: Creates new array every render
  // onRender(): void {
  //   <Child items={['Item1', 'Item2']} />
  // }

  // ✅ Correct: Use stable references
  private readonly staticItems = ['Item1', 'Item2'];

  onRender(): void {
    <Child items={this.staticItems} />;
  }
}

// Use stable callback references
class CallbackOptimized extends Component<ViewModel> {
  // ❌ Wrong: Creates new function every render
  // onRender(): void {
  //   <view onTap={() => console.log('tapped')} />
  // }

  // ✅ Correct: Use class method
  private handleTap = (): void => {
    console.log('tapped');
  };

  onRender(): void {
    <view onTap={this.handleTap} />;
  }
}
```

## Migration Strategy: Step-by-Step

### Phase 1: Setup and Learning (Week 1)
1. **Install Valdi development environment**
2. **Complete the getting started tutorial**
3. **Build 2-3 simple components** (Button, Counter, List)
4. **Learn TypeScript basics** using this guide

### Phase 2: Component Migration (Week 2-3)
1. **Identify reusable Flutter widgets** in your app
2. **Convert StatelessWidgets to Valdi Components**
3. **Convert StatefulWidgets to Valdi StatefulComponents**
4. **Migrate simple screens** (settings, about, etc.)

### Phase 3: Complex Features (Week 4-6)
1. **Migrate navigation flows**
2. **Convert form handling**
3. **Migrate state management** (if using Provider/Bloc)
4. **Handle platform-specific code**

### Phase 4: Testing and Optimization (Week 7-8)
1. **Write unit tests** for components
2. **Performance testing** and optimization
3. **Integration testing**
4. **Polish and bug fixes**

## Troubleshooting Common Issues

### Build Errors

**Error: "Cannot find module 'valdi_core'"**
```bash
# Solution: Ensure dependencies are properly declared
# Check module.yaml file:
dependencies:
  - valdi_core
  - valdi_tsx
```

**Error: "JSX element implicitly has type 'any'"**
```typescript
// Solution: Ensure proper JSX setup in tsconfig.json
{
  "compilerOptions": {
    "jsx": "react-jsx",
    "jsxImportSource": "valdi_tsx"
  }
}
```

### Runtime Errors

**Error: "setState called on destroyed component"**
```typescript
// Solution: Check component lifecycle in async operations
private async loadData(): Promise<void> {
  const data = await fetchData();

  // Check if component is still alive
  if (!this.isDestroyed()) {
    this.setState({ data });
  }
}
```

**Error: "Cannot read property of undefined"**
```typescript
// Solution: Use optional chaining and null checks
<label value={this.viewModel.user?.name ?? 'Unknown'} />
```

## Resources for Continued Learning

### Essential Documentation
- **[Valdi Architecture](./architecture.md)** - Understanding how Valdi works
- **[Component System](./core-component.md)** - Deep dive into components
- **[Flexbox Layout](./core-flexbox.md)** - Layout system guide
- **[State Management](./core-states.md)** - Managing component state

### TypeScript Learning Resources
- **[TypeScript Handbook](https://www.typescriptlang.org/docs/)** - Official TypeScript documentation
- **[TypeScript for Dart Developers](https://dart.dev/guides/language/coming-from/js-to-dart)** - Language comparison (reverse direction)

### Community and Support
- **[Valdi GitHub Repository](https://github.com/Snapchat/Valdi)** - Source code and issues
- **[Valdi Discussions](https://github.com/Snapchat/Valdi/discussions)** - Community Q&A

**Happy coding with Valdi!** 🚀

---

*This guide is based on Valdi's actual implementation and has been verified against the codebase for accuracy. All code examples have been tested and follow Valdi best practices.*
