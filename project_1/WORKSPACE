workspace(name = "project_1")

load("@bazel_tools//tools/build_defs/repo:git.bzl", "git_repository")

# Replace to http_archive once the git repo is public.
git_repository(
    name = "valdi",
    branch = "main",
    remote = "**************:Snapchat/Valdi.git",
)

git_repository(
    name = "valdi_widgets",
    branch = "main",
    remote = "**************:Snapchat/Valdi_Widgets.git",
)

load("@valdi//bzl:workspace_prepare.bzl", "valdi_prepare_workspace")

valdi_prepare_workspace()

load("@valdi//bzl:workspace_preinit.bzl", "valdi_preinitialize_workspace")

valdi_preinitialize_workspace()

load("@valdi//bzl:workspace_init.bzl", "valdi_initialize_workspace")

valdi_initialize_workspace()

load("@valdi//bzl:workspace_postinit.bzl", "valdi_post_initialize_workspace")

valdi_post_initialize_workspace()
