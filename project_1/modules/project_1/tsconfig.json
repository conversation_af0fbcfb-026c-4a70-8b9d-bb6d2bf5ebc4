{"extends": "../../bazel-project_1/external/valdi/modules/_configs/base.tsconfig.json", "compilerOptions": {"paths": {"project_1/*": ["./*", "../../bazel-out/darwin_arm64-fastbuild/bin/modules/project_1/.valdi_build/projectsync/generated_ts/project_1/*"], "jasmine/*": ["../../bazel-project_1/external/valdi/src/valdi_modules/src/valdi/jasmine/*"], "coreutils/*": ["../../bazel-project_1/external/valdi/src/valdi_modules/src/valdi/coreutils/*"], "source_map/*": ["../../bazel-project_1/external/valdi/src/valdi_modules/src/valdi/source_map/*"], "valdi_tsx/*": ["../../bazel-project_1/external/valdi/src/valdi_modules/src/valdi/valdi_tsx/*"], "valdi_core/*": ["../../bazel-project_1/external/valdi/src/valdi_modules/src/valdi/valdi_core/*"], "tslib": ["../../bazel-project_1/external/valdi/src/valdi_modules/src/valdi/valdi_core/src/tslib"]}, "types": ["../../bazel-project_1/external/valdi/modules/types/Long", "../../bazel-project_1/external/valdi/modules/types/globals"], "rootDirs": ["..", "../../bazel-out/darwin_arm64-fastbuild/bin/modules/project_1/.valdi_build/projectsync/generated_ts"]}}