import { StatefulComponent } from 'valdi_core/src/Component';
import { Label, View } from 'valdi_tsx/src/NativeTemplateElements';
import { Style } from 'valdi_core/src/Style';
import { systemBoldFont, systemFont } from 'valdi_core/src/SystemFont';
import { Device } from 'valdi_core/src/Device';
import { AnimationCurve } from 'valdi_core/src/AnimationOptions';

/**
 * @ViewModel
 * @ExportModel
 */
export interface SimpleAppViewModel {}

/**
 * @Context
 * @ExportModel
 */
export interface SimpleAppComponentContext {}

interface State {
  scale: number;
  opacity: number;
  backgroundColor: string;
  borderRadius: number;
  position: { x: number; y: number };
  isAnimating: boolean;
  width: number;
  height: number;
}

/**
 * @Component
 * @ExportModel
 */
export class SimpleApp extends StatefulComponent<SimpleAppViewModel, SimpleAppComponentContext> {

  state: State = {
    scale: 1.0,
    opacity: 1.0,
    backgroundColor: '#FF6B6B',
    borderRadius: 12,
    position: { x: 0, y: 0 },
    isAnimating: false,
    width: 150,
    height: 150,
  };

  onCreate(): void {
    console.log('🎬 Simple Valdi Animation - Ready!');
  }

  onDestroy(): void {
    // Cleanup if needed
  }

  onRender(): void {
    return <view style={styles.container}>
      <view style={styles.main}>
        <label style={styles.title} value="Simple Animation" />
        
        {/* Animated Box */}
        <view style={this.getAnimatedBoxStyle()} onTap={this.handleReset}>
          <label style={styles.boxText} value="TEST" />
        </view>
        
        {/* Control Buttons */}
        <view style={styles.buttonContainer}>
          <view style={styles.buttonWrapper} onTap={this.handleScaleUp}>
            <label style={styles.buttonText} value="Scale Up" />
          </view>
          <view style={styles.buttonWrapper} onTap={this.handleScaleDown}>
            <label style={styles.buttonText} value="Scale Down" />
          </view>
          <view style={styles.buttonWrapper} onTap={this.handleChangeColor}>
            <label style={styles.buttonText} value="Change Color" />
          </view>
          <view style={styles.buttonWrapper} onTap={this.handleStretch}>
            <label style={styles.buttonText} value="Stretch" />
          </view>
          <label
            style={styles.button}
            value="Pulse"
            onTap={this.handlePulse}
          />
          <label
            style={styles.button}
            value="Move Around"
            onTap={this.handleMoveAround}
          />
          <label
            style={styles.button}
            value="Morph Shape"
            onTap={this.handleMorphShape}
          />
          <label
            style={styles.button}
            value="Fade Out"
            onTap={this.handleFadeOut}
          />
          <label
            style={styles.button}
            value="Rainbow"
            onTap={this.handleRainbow}
          />
          <label
            style={styles.button}
            value="Combo"
            onTap={this.handleCrazyCombo}
          />
          <label
            style={styles.button}
            value="Reset All"
            onTap={this.handleReset}
          />
        </view>
      </view>
    </view>;
  }

  // Event handlers
  private handleScaleUp = (): void => {
    console.log('🔴 Scale Up tapped!');
    this.setStateAnimated({ scale: 1.5 }, {
      duration: 0.3,
      curve: AnimationCurve.EaseOut
    });
  };

  private handleScaleDown = (): void => {
    console.log('🟢 Scale Down tapped!');
    this.setStateAnimated({ scale: 0.7 }, {
      duration: 0.3,
      curve: AnimationCurve.EaseIn
    });
  };

  private handleChangeColor = (): void => {
    console.log('🔵 Change Color tapped!');
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];
    const currentIndex = colors.indexOf(this.state.backgroundColor);
    const nextIndex = (currentIndex + 1) % colors.length;
    
    this.setStateAnimated({ backgroundColor: colors[nextIndex] }, {
      duration: 0.5,
      curve: AnimationCurve.EaseInOut
    });
  };

  private handleStretch = (): void => {
    console.log('📏 Stretch tapped!');
    const isStretched = this.state.width !== this.state.height;
    this.setStateAnimated({
      width: isStretched ? 150 : 250,
      height: isStretched ? 150 : 100
    }, {
      duration: 0.6,
      curve: AnimationCurve.EaseInOut
    });
  };

  private handlePulse = (): void => {
    console.log('� Spin & Scale tapped!');
    this.setStateAnimated({
      scale: 1.3,
      opacity: 0.8
    }, {
      duration: 0.3,
      curve: AnimationCurve.EaseOut
    });

    // Then pulse back
    setTimeout(() => {
      this.setStateAnimated({
        scale: 1.0,
        opacity: 1.0
      }, {
        duration: 0.3,
        curve: AnimationCurve.EaseIn
      });
    }, 300);
  };

  private handleMoveAround = (): void => {
    console.log('🎯 Move Around tapped!');
    const newX = (Math.random() - 0.5) * 100;
    const newY = (Math.random() - 0.5) * 50;
    this.setStateAnimated({
      position: { x: newX, y: newY }
    }, {
      duration: 0.8,
      curve: AnimationCurve.EaseOut
    });
  };

  private handleMorphShape = (): void => {
    console.log('�🔄 Morph Shape tapped!');
    const newRadius = this.state.borderRadius === 12 ? 75 : 12;
    this.setStateAnimated({
      borderRadius: newRadius
    }, {
      duration: 0.6,
      curve: AnimationCurve.EaseInOut
    });
  };

  private handleFadeOut = (): void => {
    console.log('👻 Fade Out tapped!');
    const newOpacity = this.state.opacity === 1.0 ? 0.2 : 1.0;
    this.setStateAnimated({
      opacity: newOpacity
    }, {
      duration: 0.7,
      curve: AnimationCurve.EaseIn
    });
  };

  private handleRainbow = (): void => {
    console.log('🌈 Rainbow tapped!');
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98FB98'];
    let colorIndex = 0;

    const animateNextColor = () => {
      if (colorIndex < colors.length) {
        this.setStateAnimated({
          backgroundColor: colors[colorIndex]
        }, {
          duration: 0.3,
          curve: AnimationCurve.EaseInOut
        });
        colorIndex++;
        setTimeout(animateNextColor, 300);
      }
    };

    animateNextColor();
  };

  private handleCrazyCombo = (): void => {
    console.log('🎪 Crazy Combo tapped!');
    this.setStateAnimated({
      isAnimating: true
    }, {
      duration: 0.1,
      curve: AnimationCurve.Linear
    });

    // Step 1: Spin and grow
    setTimeout(() => {
      this.setStateAnimated({

        scale: 1.8,
        backgroundColor: '#FF6B6B'
      }, {
        duration: 0.4,
        curve: AnimationCurve.EaseOut
      });
    }, 100);

    // Step 2: Change shape and color
    setTimeout(() => {
      this.setStateAnimated({
        borderRadius: 0,
        backgroundColor: '#4ECDC4'
      }, {
        duration: 0.3,
        curve: AnimationCurve.EaseInOut
      });
    }, 600);

    // Step 3: Move and fade
    setTimeout(() => {
      this.setStateAnimated({
        position: { x: 50, y: -30 },
        opacity: 0.5,
        backgroundColor: '#45B7D1'
      }, {
        duration: 0.5,
        curve: AnimationCurve.EaseIn
      });
    }, 1000);

    // Step 4: Final transformation
    setTimeout(() => {
      this.setStateAnimated({

        scale: 0.8,
        borderRadius: 40,
        backgroundColor: '#96CEB4'
      }, {
        duration: 0.6,
        curve: AnimationCurve.EaseOut
      });
    }, 1600);

    // Step 5: Return to normal
    setTimeout(() => {
      this.setStateAnimated({
        scale: 1.0,
        opacity: 1.0,
        position: { x: 0, y: 0 },
        borderRadius: 12,
        backgroundColor: '#FF6B6B',
        isAnimating: false
      }, {
        duration: 0.8,
        curve: AnimationCurve.EaseInOut
      });
    }, 2400);
  };

  private handleReset = (): void => {
    console.log('🔄 Reset All tapped!');
    this.setStateAnimated({
      scale: 1.0,
      opacity: 1.0,
      backgroundColor: '#FF6B6B',
      borderRadius: 12,
      position: { x: 0, y: 0 },
      isAnimating: false,
      width: 150,
      height: 150
    }, {
      duration: 0.6,
      curve: AnimationCurve.EaseInOut
    });
  };

  // Style methods
  private getAnimatedBoxStyle(): Style<View> {
    const width = Math.round(this.state.width * this.state.scale);
    const height = Math.round(this.state.height * this.state.scale);
    return new Style<View>({
      width: width,
      height: height,
      backgroundColor: this.state.backgroundColor,
      opacity: this.state.opacity,
      borderRadius: this.state.borderRadius,
      justifyContent: 'center',
      alignItems: 'center',
      margin: 20,
      marginLeft: 20 + this.state.position.x,
      marginTop: 20 + this.state.position.y,
    });
  }
}

const styles = {
  container: new Style<View>({
    backgroundColor: '#F8F9FA',
    width: '100%',
    height: '100%',
    paddingTop: Device.getDisplayTopInset(),
    paddingBottom: Device.getDisplayBottomInset(),
    paddingLeft: Device.getDisplayLeftInset(),
    paddingRight: Device.getDisplayRightInset(),
  }),

  main: new Style<View>({
    width: '100%',
    height: '100%',
    padding: 20,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  }),

  title: new Style<Label>({
    color: '#2C3E50',
    font: systemBoldFont(24),
    textAlign: 'center',
    marginBottom: 30,
  }),

  boxText: new Style<Label>({
    color: 'white',
    font: systemBoldFont(16),
    textAlign: 'center',
  }),

  buttonContainer: new Style<View>({
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 30,
    paddingLeft: 10,
    paddingRight: 10,
    width: '100%',
  }),

  button: new Style<Label>({
    backgroundColor: '#34495E',
    color: 'white',
    font: systemFont(12),
    textAlign: 'center',
    margin: 8,
    borderRadius: 6,
    minWidth: 80,
    height: 32,
  }),

  buttonWrapper: new Style<View>({
    backgroundColor: '#34495E',
    borderRadius: 6,
    margin: 4,
    paddingTop: 8,
    paddingBottom: 8,
    paddingLeft: 12,
    paddingRight: 12,
    minWidth: 80,
    justifyContent: 'center',
    alignItems: 'center',
  }),

  buttonText: new Style<Label>({
    color: 'white',
    font: systemFont(12),
    textAlign: 'center',
  }),
};
