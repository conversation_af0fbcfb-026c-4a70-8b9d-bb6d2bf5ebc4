load("@valdi//bzl/valdi:valdi_application.bzl", "valdi_application")
load("@valdi//bzl/valdi:valdi_exported_library.bzl", "valdi_exported_library")

valdi_application(
    name = "project_1_app",
    ios_bundle_id = "com.website.project_1",
    ios_families = ["iphone"],
    root_component_path = "App@project_1/src/App",
    title = "project_1",
    version = "1.0.0",
    deps = ["//modules/project_1"],
)

valdi_exported_library(
    name = "project_1_export",
    ios_bundle_id = "com.website.project_1.lib",
    ios_bundle_name = "Project1",
    deps = ["//modules/project_1"],
)
